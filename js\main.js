// Simple JavaScript for the static site
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if(targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if(targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add active class to nav items on scroll
    const sections = document.querySelectorAll('section');
    const navLinks = document.querySelectorAll('nav ul li a');
    
    window.addEventListener('scroll', () => {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if(pageYOffset >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if(link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
});

// Resources section functionality
function showCategory(categoryId) {
    // Hide all category contents
    const allCategories = document.querySelectorAll('.category-content');
    allCategories.forEach(category => {
        category.classList.remove('active');
    });
    
    // Remove active class from all tabs
    const allTabs = document.querySelectorAll('.tab-btn');
    allTabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected category
    const selectedCategory = document.getElementById(categoryId);
    if (selectedCategory) {
        selectedCategory.classList.add('active');
    }
    
    // Add active class to clicked tab
    const clickedTab = event.target;
    clickedTab.classList.add('active');
}

// Copy RSS URL to clipboard
function copyRSSUrl() {
    const rssUrl = window.location.origin + '/rss.xml';
    
    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(rssUrl).then(() => {
            showCopyFeedback('RSS URL copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy RSS URL: ', err);
            fallbackCopyTextToClipboard(rssUrl);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(rssUrl);
    }
}

// Fallback copy method for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopyFeedback('RSS URL copied to clipboard!');
        } else {
            showCopyFeedback('Failed to copy RSS URL', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showCopyFeedback('Failed to copy RSS URL', 'error');
    }
    
    document.body.removeChild(textArea);
}

// Show copy feedback to user
function showCopyFeedback(message, type = 'success') {
    // Remove existing feedback if any
    const existingFeedback = document.querySelector('.copy-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = `copy-feedback ${type}`;
    feedback.textContent = message;
    feedback.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : '#f44336'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-weight: 500;
        animation: slideInRight 0.3s ease-out;
    `;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(feedback);
    
    // Remove feedback after 3 seconds
    setTimeout(() => {
        feedback.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 300);
    }, 3000);
}

// Show RSS subscription options modal
function showRSSOptions() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

// Close RSS modal
function closeRSSModal() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scrolling
    }
}

// Copy RSS URL from input field
function copyFromInput() {
    const input = document.getElementById('rss-url-input');
    if (input) {
        input.select();
        input.setSelectionRange(0, 99999); // For mobile devices
        
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(input.value).then(() => {
                showCopyFeedback('RSS URL copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy RSS URL: ', err);
                fallbackCopyTextToClipboard(input.value);
            });
        } else {
            fallbackCopyTextToClipboard(input.value);
        }
    }
}

// Load RSS feed dynamically
async function loadRSSFeed() {
    const feedPreview = document.getElementById('rss-feed-preview');
    if (!feedPreview) return;

    try {
        // Fetch the RSS feed
        const response = await fetch('/rss.xml');
        const xmlText = await response.text();
        
        // Parse the XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
        
        // Extract items from RSS
        const items = xmlDoc.querySelectorAll('item');
        
        // Clear existing content
        feedPreview.innerHTML = '';
        
        // Display first 2 items (most recent)
        const itemsToShow = Array.from(items).slice(0, 2);
        
        itemsToShow.forEach(item => {
            const title = item.querySelector('title')?.textContent || 'No title';
            const pubDate = item.querySelector('pubDate')?.textContent || '';
            const description = item.querySelector('description')?.textContent || '';
            const link = item.querySelector('link')?.textContent || '#';
            
            // Format date
            const formattedDate = formatRSSDate(pubDate);
            
            // Extract plain text from description (remove HTML)
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = description;
            const plainDescription = tempDiv.textContent || tempDiv.innerText || '';
            
            // Truncate description to ~100 characters
            const truncatedDescription = plainDescription.length > 100 
                ? plainDescription.substring(0, 100) + '...' 
                : plainDescription;
            
            // Create feed item element
            const feedItem = document.createElement('div');
            feedItem.className = 'feed-item';
            feedItem.innerHTML = `
                <h5><a href="${link}" target="_blank">${title}</a></h5>
                <p class="feed-date">${formattedDate}</p>
                <p class="feed-excerpt">${truncatedDescription}</p>
            `;
            
            feedPreview.appendChild(feedItem);
        });
        
        console.log('RSS feed loaded successfully');
        
    } catch (error) {
        console.error('Failed to load RSS feed:', error);
        
        // Fallback to hardcoded content if RSS loading fails
        feedPreview.innerHTML = `
            <div class="feed-item">
                <h5>Building Scalable Microservices with Spring Boot</h5>
                <p class="feed-date">January 15, 2025</p>
                <p class="feed-excerpt">Learn how to design and implement microservices architecture using Spring Boot, Docker, and Kubernetes...</p>
            </div>
            <div class="feed-item">
                <h5>DevOps Best Practices for Backend Developers</h5>
                <p class="feed-date">January 10, 2025</p>
                <p class="feed-excerpt">Essential DevOps practices every backend developer should know, from CI/CD to monitoring...</p>
            </div>
        `;
    }
}

// Format RSS date to readable format
function formatRSSDate(rssDate) {
    if (!rssDate) return '';
    
    try {
        const date = new Date(rssDate);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (error) {
        return rssDate; // Return original if parsing fails
    }
}

// Close modal when clicking outside of it
document.addEventListener('click', function(event) {
    const modal = document.getElementById('rss-modal');
    if (modal && event.target === modal) {
        closeRSSModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeRSSModal();
    }
});

// Initialize RSS feed on page load
document.addEventListener('DOMContentLoaded', function() {
    loadRSSFeed();
});
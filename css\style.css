:root {
    --primary-color: #3a86ff;
    --secondary-color: #0a2463;
    --accent-color: #ff9e00;
    --text-color: #2b2d42;
    --light-text: #6c757d;
    --background: #ffffff;
    --light-background: #f8f9fa;
    --dark-background: #1c1e26;
    --border-color: #e9ecef;
    --font-main: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Montserrat', sans-serif;
    --font-code: 'Fira Code', monospace;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-main);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--background);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.logo {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    position: relative;
    font-family: var(--font-heading);
}

.logo::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.logo:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
    padding: 5px 0;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

nav ul li a:hover::after,
nav ul li a.active::after {
    transform: scaleX(1);
    transform-origin: left;
}

nav ul li a:hover,
nav ul li a.active {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    padding: 180px 0 120px;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 30%, rgba(58, 134, 255, 0.1) 0%, transparent 20%),
        radial-gradient(circle at 80% 70%, rgba(255, 158, 0, 0.1) 0%, transparent 20%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.construction-badge {
    display: inline-block;
    background: linear-gradient(90deg, var(--accent-color), #ffb700);
    color: #000;
    padding: 10px 20px;
    border-radius: 30px;
    font-weight: 600;
    margin-bottom: 40px;
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 20px rgba(255, 158, 0, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
    }
}

.hero h1 {
    font-size: 56px;
    margin-bottom: 15px;
    font-family: var(--font-heading);
    font-weight: 800;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.hero h2 {
    font-size: 28px;
    color: var(--primary-color);
    margin-bottom: 25px;
    font-weight: 600;
}

.hero p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto 40px;
    color: var(--text-color);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.btn {
    display: inline-block;
    padding: 14px 28px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    transform: translateX(-100%);
    transition: transform 0.6s;
    z-index: -1;
}

.btn:hover::before {
    transform: translateX(0);
}

.btn.primary {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 4px 15px rgba(58, 134, 255, 0.3);
}

.btn.primary:hover {
    box-shadow: 0 6px 20px rgba(58, 134, 255, 0.4);
    transform: translateY(-2px);
}

.btn.secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 15px rgba(58, 134, 255, 0.1);
}

.btn.secondary:hover {
    background-color: rgba(58, 134, 255, 0.05);
    box-shadow: 0 6px 20px rgba(58, 134, 255, 0.2);
    transform: translateY(-2px);
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Section Styling */
section {
    padding: 100px 0;
    position: relative;
}

.section-title {
    font-size: 36px;
    margin-bottom: 50px;
    text-align: center;
    font-family: var(--font-heading);
    font-weight: 700;
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.coming-soon {
    background-color: var(--light-background);
    border: 2px dashed var(--border-color);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    color: var(--light-text);
    font-style: italic;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.03);
}

/* Dino Game Section */
.dino-game-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    position: relative;
    overflow: hidden;
}

.dino-game-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(58, 134, 255, 0.08) 0%, transparent 25%),
        radial-gradient(circle at 75% 75%, rgba(255, 158, 0, 0.08) 0%, transparent 25%);
    z-index: 1;
}

.game-container {
    position: relative;
    z-index: 2;
    background-color: var(--background);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(58, 134, 255, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 20px;
}

.score-display {
    display: flex;
    gap: 30px;
    font-family: var(--font-code);
    font-weight: 600;
    font-size: 18px;
    color: var(--secondary-color);
}

.game-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.game-controls p {
    margin: 0;
    color: var(--light-text);
    font-size: 14px;
}

.game-controls kbd {
    background-color: var(--light-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 2px 6px;
    font-family: var(--font-code);
    font-size: 12px;
    color: var(--text-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

#dino-canvas {
    width: 100%;
    max-width: 800px;
    height: 200px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 70%, #DEB887 70%, #DEB887 100%);
    display: block;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;
}

#dino-canvas:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(58, 134, 255, 0.2);
}

.game-over-screen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 2px solid var(--primary-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.game-over-screen h3 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-family: var(--font-heading);
}

.game-over-screen p {
    margin-bottom: 20px;
    font-family: var(--font-code);
    font-size: 18px;
    color: var(--text-color);
}

/* Game canvas animation */
@keyframes gameReady {
    0% {
        transform: scale(0.95);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.02);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

#dino-canvas.game-active {
    animation: gameReady 0.5s ease-out;
}

/* Score display animations */
.score-display span {
    transition: all 0.3s ease;
}

.score-display span:hover {
    color: var(--primary-color);
    transform: scale(1.05);
}

/* Game controls styling */
.game-controls {
    align-items: center;
}

#start-game, #restart-game {
    min-width: 120px;
    font-size: 14px;
    padding: 10px 20px;
}

/* Mobile touch improvements */
@media (max-width: 768px) {
    #dino-canvas {
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
}

/* About Section */
.about {
    background-color: var(--background);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    margin-bottom: 25px;
    font-size: 17px;
    line-height: 1.7;
}

.skills {
    background-color: var(--light-background);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.skills h3 {
    margin-bottom: 20px;
    font-family: var(--font-heading);
    color: var(--secondary-color);
}

.skill-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.skill-list li {
    padding: 10px 18px;
    background: linear-gradient(135deg, rgba(58, 134, 255, 0.1), rgba(10, 36, 99, 0.1));
    border-radius: 30px;
    display: inline-block;
    font-weight: 500;
    transition: all 0.3s;
    border: 1px solid rgba(58, 134, 255, 0.2);
}

.skill-list li:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(58, 134, 255, 0.1);
    background: linear-gradient(135deg, rgba(58, 134, 255, 0.15), rgba(10, 36, 99, 0.15));
}

/* Portfolio Section */
.portfolio {
    background-color: var(--light-background);
    position: relative;
    overflow: hidden;
}

.portfolio::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 10% 10%, rgba(58, 134, 255, 0.05) 0%, transparent 20%),
        radial-gradient(circle at 90% 90%, rgba(255, 158, 0, 0.05) 0%, transparent 20%);
    z-index: 1;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 2;
}

.portfolio-item {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background);
    transition: all 0.3s;
    position: relative;
    z-index: 1;
}

.portfolio-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    z-index: 2;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-content {
    padding: 30px;
    text-align: center;
}

.portfolio-content h3 {
    font-family: var(--font-heading);
    margin-bottom: 15px;
    color: var(--secondary-color);
}

/* Resources Section */
.resources {
    background-color: var(--light-background);
    position: relative;
    overflow: hidden;
}

.resources::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 15% 20%, rgba(58, 134, 255, 0.05) 0%, transparent 25%),
        radial-gradient(circle at 85% 80%, rgba(255, 158, 0, 0.05) 0%, transparent 25%);
    z-index: 1;
}

.resources-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    position: relative;
    z-index: 2;
}

.rss-section {
    background-color: var(--background);
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.rss-section h3 {
    font-family: var(--font-heading);
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.rss-section h3::before {
    content: '📡';
    font-size: 20px;
}

.rss-section p {
    margin-bottom: 25px;
    color: var(--light-text);
    line-height: 1.6;
}

.rss-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.rss-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.recent-posts h4 {
    font-family: var(--font-heading);
    margin-bottom: 20px;
    color: var(--secondary-color);
    font-size: 18px;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.feed-preview {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.feed-item {
    padding: 20px;
    background-color: var(--light-background);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s;
}

.feed-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(58, 134, 255, 0.1);
}

.feed-item h5 {
    font-family: var(--font-heading);
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--secondary-color);
    line-height: 1.4;
}

.feed-date {
    font-size: 12px;
    color: var(--light-text);
    margin-bottom: 10px;
    font-weight: 500;
}

.feed-excerpt {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
}

.bookmarks-section {
    background-color: var(--background);
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

.bookmarks-section h3 {
    font-family: var(--font-heading);
    font-size: 24px;
    margin-bottom: 30px;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.bookmarks-section h3::before {
    content: '🔖';
    font-size: 20px;
}

.category-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 30px;
    background-color: var(--light-background);
    padding: 5px;
    border-radius: 12px;
    overflow-x: auto;
}

.tab-btn {
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    color: var(--light-text);
    transition: all 0.3s;
    white-space: nowrap;
    font-family: var(--font-main);
}

.tab-btn.active,
.tab-btn:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 4px 12px rgba(58, 134, 255, 0.2);
}

.bookmark-content {
    position: relative;
}

.category-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.category-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bookmark-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
}

.bookmark-item {
    background-color: var(--light-background);
    padding: 25px;
    border-radius: 12px;
    transition: all 0.3s;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.bookmark-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s;
}

.bookmark-item:hover::before {
    transform: scaleX(1);
}

.bookmark-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.bookmark-item h4 {
    margin-bottom: 12px;
    font-family: var(--font-heading);
    font-size: 16px;
}

.bookmark-item h4 a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s;
}

.bookmark-item h4 a:hover {
    color: var(--primary-color);
}

.bookmark-item p {
    font-size: 14px;
    line-height: 1.5;
    color: var(--light-text);
    margin-bottom: 15px;
}

.bookmark-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: linear-gradient(135deg, rgba(58, 134, 255, 0.1), rgba(10, 36, 99, 0.1));
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(58, 134, 255, 0.2);
    transition: all 0.3s;
}

.tag:hover {
    background: linear-gradient(135deg, rgba(58, 134, 255, 0.15), rgba(10, 36, 99, 0.15));
    transform: scale(1.05);
}

/* RSS Modal Styles */
.rss-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.rss-modal-content {
    background-color: var(--background);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
    position: relative;
    z-index: 100000;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.rss-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 2px solid var(--border-color);
}

.rss-modal-header h4 {
    font-family: var(--font-heading);
    color: var(--secondary-color);
    margin: 0;
    font-size: 20px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--light-text);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.close-modal:hover {
    background-color: var(--light-background);
    color: var(--text-color);
}

.rss-modal-body {
    padding: 30px;
}

.rss-modal-body p {
    margin-bottom: 25px;
    color: var(--light-text);
}

.rss-options {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 30px;
}

.rss-option h5 {
    font-family: var(--font-heading);
    margin-bottom: 15px;
    color: var(--secondary-color);
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.rss-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.rss-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background-color: var(--light-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    cursor: pointer;
}

.rss-link:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(58, 134, 255, 0.2);
}

.rss-url-display {
    background-color: var(--light-background);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.rss-url-display label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 14px;
}

.url-input-group {
    display: flex;
    gap: 10px;
}

.url-input-group input {
    flex: 1;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-code);
    font-size: 13px;
    background-color: var(--background);
    color: var(--text-color);
}

.copy-btn {
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(58, 134, 255, 0.3);
}

/* Contact Section */
.contact {
    background-color: var(--background);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.contact-info {
    padding: 30px;
    background-color: var(--light-background);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.contact-info p {
    margin-bottom: 25px;
    font-size: 17px;
    line-height: 1.7;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    color: white;
    transition: all 0.3s;
    box-shadow: 0 5px 15px rgba(58, 134, 255, 0.2);
}

.social-link:hover {
    transform: translateY(-5px) rotate(10deg);
    box-shadow: 0 8px 20px rgba(58, 134, 255, 0.3);
}

/* Footer */
footer {
    background-color: var(--dark-background);
    padding: 50px 0;
    text-align: center;
    color: white;
}

.development-note {
    margin-top: 15px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav ul {
        display: none;
    }
    
    .about-content,
    .contact-content,
    .resources-content {
        grid-template-columns: 1fr;
    }
    
    .hero h1 {
        font-size: 42px;
    }
    
    .hero h2 {
        font-size: 24px;
    }
    
    .section-title {
        font-size: 32px;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }
    
    .rss-section {
        position: static;
        margin-bottom: 30px;
    }
    
    .category-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 80px;
    }
    
    .bookmark-grid {
        grid-template-columns: 1fr;
    }
    
    .rss-actions {
        gap: 10px;
    }
    
    .rss-actions .btn {
        max-width: none;
    }

    /* Dino Game Responsive */
    .game-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 15px;
    }

    .score-display {
        flex-direction: column;
        gap: 10px;
        font-size: 16px;
    }

    .game-controls {
        flex-direction: column;
        gap: 15px;
    }

    #dino-canvas {
        height: 150px;
        max-width: 100%;
    }

    .game-over-screen {
        padding: 20px;
        width: 90%;
        max-width: 300px;
    }

    .game-over-screen h3 {
        font-size: 20px;
    }

    .game-over-screen p {
        font-size: 16px;
    }
}
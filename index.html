<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nob <PERSON> | Senior Backend Developer</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦕</text></svg>">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="container">
            <a href="#" class="logo">NH</a>
            <nav>
                <ul>
                    <li><a href="#about">About</a></li>
                    <li><a href="#portfolio">Portfolio</a></li>
                    <li><a href="#resume">Resume</a></li>
                    <li><a href="#resources">Resources</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="construction-badge">🚧 Site Under Construction 🚧</div>
                <h1>Nob Hokleng</h1>
                <h2>Senior Backend Developer</h2>
                <p>Building scalable systems and solving complex technical challenges for 6+ years</p>
                <div class="cta-buttons">
                    <a href="#portfolio" class="btn primary">View Portfolio</a>
                    <a href="#contact" class="btn secondary">Contact Me</a>
                </div>
            </div>
        </div>
    </section>

    <section id="dino-game" class="dino-game-section">
        <div class="container">
            <h2 class="section-title">Chrome Dino Game</h2>
            <div class="game-container">
                <div class="game-info">
                    <div class="score-display">
                        <span>Score: <span id="score">0</span></span>
                        <span>High Score: <span id="high-score">0</span></span>
                    </div>
                    <div class="game-controls">
                        <p>🦕 Press <kbd>SPACE</kbd> or <kbd>CLICK</kbd> to jump over cacti!</p>
                        <button id="start-game" class="btn primary">🎮 Start Game</button>
                    </div>
                </div>
                <canvas id="dino-canvas" width="800" height="200"></canvas>
                <div id="game-over" class="game-over-screen" style="display: none;">
                    <h3>Game Over!</h3>
                    <p>Your Score: <span id="final-score">0</span></p>
                    <button id="restart-game" class="btn primary">Play Again</button>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>Senior backend developer with 6 years of experience designing and implementing scalable systems. Specialized in backend architecture, DevOps, and technical leadership.</p>
                    <p class="coming-soon">Full professional journey coming soon...</p>
                </div>
                <div class="skills">
                    <h3>Core Skills</h3>
                    <ul class="skill-list">
                        <li>Backend Development</li>
                        <li>System Architecture</li>
                        <li>DevOps & CI/CD</li>
                        <li>Technical Leadership</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <section id="portfolio" class="portfolio">
        <div class="container">
            <h2 class="section-title">Portfolio</h2>
            <div class="portfolio-grid">
                <div class="portfolio-item coming-soon">
                    <div class="portfolio-content">
                        <h3>Project 1</h3>
                        <p>Detailed case study coming soon</p>
                    </div>
                </div>
                <div class="portfolio-item coming-soon">
                    <div class="portfolio-content">
                        <h3>Project 2</h3>
                        <p>Detailed case study coming soon</p>
                    </div>
                </div>
                <div class="portfolio-item coming-soon">
                    <div class="portfolio-content">
                        <h3>Project 3</h3>
                        <p>Detailed case study coming soon</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="resume" class="resume">
        <div class="container">
            <h2 class="section-title">Resume</h2>
            <p class="coming-soon">Interactive resume experience coming soon</p>
            <a href="#" class="btn secondary disabled">Download PDF (Coming Soon)</a>
        </div>
    </section>

    <section id="resources" class="resources">
        <div class="container">
            <h2 class="section-title">Resources & RSS Feed</h2>
            <div class="resources-content">
                <div class="rss-section">
                    <h3>My RSS Feed</h3>
                    <p>Stay updated with my latest articles, tutorials, and tech insights</p>
                    <div class="rss-actions">
                        <button class="btn secondary rss-btn" onclick="showRSSOptions()">
                            <i class="fas fa-rss"></i> Subscribe to RSS
                        </button>
                        <button class="btn primary" onclick="copyRSSUrl()">
                            <i class="fas fa-copy"></i> Copy RSS URL
                        </button>
                    </div>
                    <div id="rss-modal" class="rss-modal" style="display: none;">
                        <div class="rss-modal-content">
                            <div class="rss-modal-header">
                                <h4>Subscribe to My RSS Feed</h4>
                                <button class="close-modal" onclick="closeRSSModal()">&times;</button>
                            </div>
                            <div class="rss-modal-body">
                                <p>Choose your preferred way to subscribe:</p>
                                <div class="rss-options">
                                    <div class="rss-option">
                                        <h5>📱 Mobile Apps</h5>
                                        <div class="rss-links">
                                            <a href="feedly://i/subscription/feed/https://nobhokleng.dev/rss.xml" class="rss-link">
                                                <i class="fas fa-external-link-alt"></i> Feedly
                                            </a>
                                            <a href="inoreader://add_feed/https://nobhokleng.dev/rss.xml" class="rss-link">
                                                <i class="fas fa-external-link-alt"></i> Inoreader
                                            </a>
                                        </div>
                                    </div>
                                    <div class="rss-option">
                                        <h5>🖥️ Desktop Apps</h5>
                                        <div class="rss-links">
                                            <button onclick="copyRSSUrl(); closeRSSModal();" class="rss-link">
                                                <i class="fas fa-copy"></i> Copy URL for your RSS reader
                                            </button>
                                        </div>
                                    </div>
                                    <div class="rss-option">
                                        <h5>🌐 Web Readers</h5>
                                        <div class="rss-links">
                                            <a href="https://feedly.com/i/subscription/feed/https://nobhokleng.dev/rss.xml" target="_blank" class="rss-link">
                                                <i class="fas fa-external-link-alt"></i> Feedly Web
                                            </a>
                                            <a href="https://www.inoreader.com/feed/https://nobhokleng.dev/rss.xml" target="_blank" class="rss-link">
                                                <i class="fas fa-external-link-alt"></i> Inoreader Web
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="rss-url-display">
                                    <label>RSS Feed URL:</label>
                                    <div class="url-input-group">
                                        <input type="text" id="rss-url-input" value="https://nobhokleng.dev/rss.xml" readonly>
                                        <button onclick="copyFromInput()" class="copy-btn">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="recent-posts">
                        <h4>Recent Posts</h4>
                        <div id="rss-feed-preview" class="feed-preview">
                            <!-- RSS feed items will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                
                <div class="bookmarks-section">
                    <h3>Curated Bookmarks</h3>
                    <div class="bookmark-categories">
                        <div class="category-tabs">
                            <button class="tab-btn active" onclick="showCategory('coding')">Coding</button>
                            <button class="tab-btn" onclick="showCategory('devops')">DevOps</button>
                            <button class="tab-btn" onclick="showCategory('architecture')">Architecture</button>
                            <button class="tab-btn" onclick="showCategory('career')">Career</button>
                        </div>
                        
                        <div class="bookmark-content">
                            <div id="coding" class="category-content active">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://spring.io/guides" target="_blank">Spring Guides</a></h4>
                                        <p>Comprehensive guides for Spring Framework development</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Java</span>
                                            <span class="tag">Spring</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://refactoring.guru" target="_blank">Refactoring Guru</a></h4>
                                        <p>Design patterns and refactoring techniques explained clearly</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Design Patterns</span>
                                            <span class="tag">Best Practices</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://github.com/donnemartin/system-design-primer" target="_blank">System Design Primer</a></h4>
                                        <p>Learn how to design large-scale systems</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">System Design</span>
                                            <span class="tag">Architecture</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="devops" class="category-content">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://docs.docker.com" target="_blank">Docker Documentation</a></h4>
                                        <p>Official Docker documentation and best practices</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Docker</span>
                                            <span class="tag">Containers</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://kubernetes.io/docs" target="_blank">Kubernetes Docs</a></h4>
                                        <p>Complete guide to container orchestration</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Kubernetes</span>
                                            <span class="tag">Orchestration</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://aws.amazon.com/architecture" target="_blank">AWS Architecture Center</a></h4>
                                        <p>Cloud architecture patterns and best practices</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">AWS</span>
                                            <span class="tag">Cloud</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="architecture" class="category-content">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://microservices.io" target="_blank">Microservices.io</a></h4>
                                        <p>Patterns for building microservices architecture</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Microservices</span>
                                            <span class="tag">Patterns</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://martinfowler.com" target="_blank">Martin Fowler's Blog</a></h4>
                                        <p>Insights on software architecture and development</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Architecture</span>
                                            <span class="tag">Thought Leadership</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://12factor.net" target="_blank">The Twelve-Factor App</a></h4>
                                        <p>Methodology for building software-as-a-service apps</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">SaaS</span>
                                            <span class="tag">Best Practices</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="career" class="category-content">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://staffeng.com" target="_blank">StaffEng</a></h4>
                                        <p>Stories and strategies for reaching Staff+ engineering roles</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Career Growth</span>
                                            <span class="tag">Leadership</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://www.levels.fyi" target="_blank">Levels.fyi</a></h4>
                                        <p>Compare career levels and compensation across tech companies</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Compensation</span>
                                            <span class="tag">Career Levels</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://github.com/kamranahmedse/developer-roadmap" target="_blank">Developer Roadmap</a></h4>
                                        <p>Step by step guides and paths to learn different tools or technologies</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Learning Path</span>
                                            <span class="tag">Skills</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Contact</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <p>Feel free to reach out if you have any questions or opportunities.</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="contact-form">
                    <p class="coming-soon">Contact form coming soon</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2025 Nob Hokleng. All rights reserved.</p>
            <p class="development-note">This site is currently under development. Expected completion: July 2025</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>